{"$schema": "https://schema.tauri.app/config/2", "productName": "HYDRA21", "version": "0.1.0-dev", "identifier": "com.hydra21.app", "build": {"beforeDevCommand": "next dev", "beforeBuildCommand": "next build", "devUrl": "http://localhost:3000", "frontendDist": "../out"}, "app": {"withGlobalTauri": true, "security": {"csp": null}, "windows": [{"title": "HYDRA²¹ - Desktop Application", "width": 1250, "height": 900, "minWidth": 900, "minHeight": 720, "titleBarStyle": "Overlay", "decorations": false, "hiddenTitle": true, "center": true, "resizable": true, "fullscreen": false, "transparent": false, "skipTaskbar": false, "alwaysOnTop": false, "contentProtected": false, "label": "main", "theme": "Dark", "visible": true, "shadow": true, "maximizable": true, "minimizable": true, "closable": true}]}, "plugins": {"shell": {"open": true}, "updater": {"active": true, "endpoints": ["https://github.com/your-repo/hydra21/releases/latest/download/latest.json"], "dialog": true}}, "bundle": {"active": true, "targets": ["msi", "nsis"], "icon": ["icons/pipe_logo.ico"]}}