'use client'

import { useEffect, useState } from 'react'

export default function TitleBarTest() {
  const [isTauri, setIsTauri] = useState(false)
  const [windowInfo, setWindowInfo] = useState<any>(null)

  useEffect(() => {
    const checkTauri = async () => {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        setIsTauri(true)
        try {
          const { getCurrentWindow } = await import('@tauri-apps/api/window')
          const win = getCurrentWindow()
          
          const info = {
            isMaximized: await win.isMaximized(),
            isFullscreen: await win.isFullscreen(),
            title: await win.title(),
          }
          setWindowInfo(info)
        } catch (error) {
          console.error('Error getting window info:', error)
        }
      }
    }

    checkTauri()
  }, [])

  if (!isTauri) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
        <h3 className="font-bold text-yellow-800">Titlebar Test - Web Mode</h3>
        <p className="text-yellow-700">Running in web browser - Tauri APIs not available</p>
      </div>
    )
  }

  return (
    <div className="p-4 bg-green-100 border border-green-400 rounded">
      <h3 className="font-bold text-green-800">Titlebar Test - Tauri Mode</h3>
      <p className="text-green-700">✅ Tauri environment detected</p>
      {windowInfo && (
        <div className="mt-2 text-sm text-green-600">
          <p>Title: {windowInfo.title}</p>
          <p>Maximized: {windowInfo.isMaximized ? 'Yes' : 'No'}</p>
          <p>Fullscreen: {windowInfo.isFullscreen ? 'Yes' : 'No'}</p>
        </div>
      )}
    </div>
  )
}
