/**
 * Daily Splash Screen Service
 * Manages showing splash screen once per day with optimized performance
 */

interface DailySplashData {
  lastShownDate: string; // ISO date string (YYYY-MM-DD)
  lastShownTimestamp: number; // Unix timestamp
  timezone: string; // User's timezone
  version: string; // App version for migration handling
}

class DailySplashService {
  private readonly STORAGE_KEY = 'hydra21_daily_splash';
  private readonly APP_VERSION = '0.1.0';
  private data: DailySplashData | null = null;
  private isInitialized = false;

  /**
   * Initialize the service - called once during app startup
   * This is synchronous and fast to avoid startup delays
   */
  initialize(): void {
    if (this.isInitialized) return;

    try {
      // Fast synchronous initialization
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.data = JSON.parse(stored);
        // Validate data structure
        if (!this.isValidData(this.data)) {
          this.data = null;
        }
      }
      this.isInitialized = true;
    } catch (error) {
      console.warn('Failed to initialize daily splash service:', error);
      this.data = null;
      this.isInitialized = true;
    }
  }

  /**
   * Check if splash screen should be shown today
   * This is the main logic - must be fast and synchronous
   */
  shouldShowSplashToday(): boolean {
    if (!this.isInitialized) {
      this.initialize();
    }

    // First time ever - show splash
    if (!this.data) {
      console.log("🌅 Daily splash: First time ever - showing splash");
      return true;
    }

    const now = new Date();
    const currentDate = this.formatDate(now);
    const currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    console.log("🌅 Daily splash check:", {
      currentDate,
      lastShownDate: this.data.lastShownDate,
      currentTimezone,
      storedTimezone: this.data.timezone,
      version: this.data.version,
      appVersion: this.APP_VERSION
    });

    // Check if it's a new day
    if (this.data.lastShownDate !== currentDate) {
      console.log("🌅 Daily splash: New day detected - showing splash");
      return true;
    }

    // Handle timezone changes
    if (this.data.timezone !== currentTimezone) {
      // Recalculate based on new timezone
      const lastShownInCurrentTz = new Date(this.data.lastShownTimestamp);
      const lastShownDateInCurrentTz = this.formatDate(lastShownInCurrentTz);

      if (lastShownDateInCurrentTz !== currentDate) {
        console.log("🌅 Daily splash: Timezone change detected - showing splash");
        return true;
      }
    }

    // Handle version changes (optional - show splash on app updates)
    if (this.data.version !== this.APP_VERSION) {
      console.log("🌅 Daily splash: Version change detected - showing splash");
      return true;
    }

    console.log("🌅 Daily splash: Already shown today - skipping");
    return false;
  }

  /**
   * Mark splash as shown for today
   * Called after splash screen is displayed
   */
  markSplashShown(): void {
    const now = new Date();
    const currentDate = this.formatDate(now);
    const currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    this.data = {
      lastShownDate: currentDate,
      lastShownTimestamp: now.getTime(),
      timezone: currentTimezone,
      version: this.APP_VERSION
    };

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.data));
    } catch (error) {
      console.warn('Failed to save daily splash data:', error);
    }
  }

  /**
   * Reset splash state (for testing or manual reset)
   */
  reset(): void {
    this.data = null;
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to reset daily splash data:', error);
    }
  }

  /**
   * Get debug information about current state
   */
  getDebugInfo(): any {
    const now = new Date();
    return {
      isInitialized: this.isInitialized,
      currentDate: this.formatDate(now),
      currentTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      storedData: this.data,
      shouldShow: this.shouldShowSplashToday()
    };
  }

  /**
   * Handle edge case: app left open overnight
   * Call this when app regains focus or at regular intervals
   */
  checkForDateChange(): boolean {
    if (!this.data) return false;

    const now = new Date();
    const currentDate = this.formatDate(now);

    // If date has changed since last check, reset for next launch
    if (this.data.lastShownDate !== currentDate) {
      // Don't show splash immediately, but prepare for next launch
      return true;
    }

    return false;
  }

  // Private helper methods

  private formatDate(date: Date): string {
    // Use local date to handle timezone correctly
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private isValidData(data: any): data is DailySplashData {
    return (
      data &&
      typeof data.lastShownDate === 'string' &&
      typeof data.lastShownTimestamp === 'number' &&
      typeof data.timezone === 'string' &&
      typeof data.version === 'string' &&
      data.lastShownDate.match(/^\d{4}-\d{2}-\d{2}$/) &&
      data.lastShownTimestamp > 0
    );
  }
}

// Export singleton instance
export const dailySplashService = new DailySplashService();
export default dailySplashService;
