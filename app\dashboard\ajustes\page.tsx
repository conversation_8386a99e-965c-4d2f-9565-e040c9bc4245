"use client";

import React, { useEffect, useState } from "react";
import {
  Cog6ToothIcon,
  ArrowPathIcon,
  ServerIcon,
} from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { FontSelector } from "@/components/font-selector";
import { storageService } from "@/lib/storage-service";
import { useTauri } from "@/contexts/TauriContext";
import { useSystemSettings } from "@/contexts/SystemSettingsContext";
import { useNotifications } from "@/contexts/NotificationContext";
import NotificationHelper from "@/lib/notification-helper";
import dailySplashService from "@/lib/daily-splash-service";
import { StandardizedSelect } from "@/components/ui/standardized-select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BellRingIcon } from "lucide-react";

// Función auxiliar para mostrar notificaciones
const mostrarNotificacion = (mensaje: string, tipo: 'success' | 'error' | 'info' = 'success', duracion: number = 2500) => {
  // Crear elemento de notificación
  const notification = document.createElement('div');

  // Definir color según el tipo
  const bgColor = tipo === 'success' ? 'bg-green-500' :
                  tipo === 'error' ? 'bg-red-500' :
                  'bg-blue-500';

  notification.className = `fixed bottom-4 right-4 ${bgColor} text-white px-4 py-2 rounded-md shadow-lg z-50`;
  notification.textContent = mensaje;
  notification.style.animation = `fadeIn 0.3s, fadeOut 0.3s ${duracion - 500}ms forwards`;

  // Agregar estilos de animación si no existen
  if (!document.getElementById('notification-styles')) {
    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(20px); }
      }
    `;
    document.head.appendChild(style);
  }

  document.body.appendChild(notification);

  // Eliminar la notificación después del tiempo especificado
  setTimeout(() => {
    notification.remove();
  }, duracion);

  return notification;
};

export default function AjustesPage() {
  const { isTauri } = useTauri();
  const { settings, updateSettings } = useSystemSettings();
  const { addNotification } = useNotifications();

  // Obtener el parámetro tab de la URL
  const [urlTab, setUrlTab] = useState<string | null>(null);

  // Efecto para obtener el parámetro tab de la URL
  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get("tab");
      if (tabParam) {
        setUrlTab(tabParam);
      }
    }
  }, []);

  // Estado y persistencia para animaciones
  const [animaciones, setAnimaciones] = useState(true);
  // Estado y persistencia para preferencias
  const [idioma, setIdioma] = useState("es");
  const [unidades, setUnidades] = useState("metric");
  const [formatoFecha, setFormatoFecha] = useState("DD/MM/YYYY");
  const [notificaciones, setNotificaciones] = useState(true);
  const [exportando, setExportando] = useState(false);
  const [eliminando, setEliminando] = useState(false);

  // Estado para configuración de notificaciones
  const [notificationSettings, setNotificationSettings] = useState({
    showSystemNotifications: true,
    soundEnabled: true,
    calculationNotifications: true,
    exportNotifications: true,
    updateNotifications: true,
    featureNotifications: true,
  });
  const [savingNotificationSettings, setSavingNotificationSettings] = useState(false);

  // Cargar preferencias desde localStorage al montar
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Cargar preferencias generales
      const anim = localStorage.getItem("pref_animaciones");
      if (anim !== null) setAnimaciones(anim === "true");
      const lang = localStorage.getItem("pref_idioma");
      if (lang) setIdioma(lang);
      const units = localStorage.getItem("pref_unidades");
      if (units) setUnidades(units);
      const dateFmt = localStorage.getItem("pref_formato_fecha");
      if (dateFmt) setFormatoFecha(dateFmt);
      const notif = localStorage.getItem("pref_notificaciones");
      if (notif !== null) setNotificaciones(notif === "true");

      // System settings are now loaded from the SystemSettings context
    }
  }, []);

  // Guardar en localStorage cuando cambian
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Guardar preferencias generales
      localStorage.setItem("pref_animaciones", String(animaciones));
      localStorage.setItem("pref_idioma", idioma);
      localStorage.setItem("pref_unidades", unidades);
      localStorage.setItem("pref_formato_fecha", formatoFecha);
      localStorage.setItem("pref_notificaciones", String(notificaciones));

      // System settings are now saved by the SystemSettings context
    }
  }, [
    animaciones, idioma, unidades, formatoFecha, notificaciones
  ]);

  // Funcionalidad: Exportar datos
  const exportarDatos = () => {
    setExportando(true);
    const datos = {
      // Preferencias generales
      animaciones,
      idioma,
      unidades,
      formatoFecha,
      notificaciones,

      // Preferencias de sistema
      sistema: {
        performanceMode: settings.performanceMode,
        displayMode: settings.displayMode,
        cacheEnabled: settings.cacheEnabled,
        developerMode: settings.developerMode
      },

      // Metadatos
      exportadoEn: new Date().toISOString(),
      version: "0.1.0"
    };

    const blob = new Blob([JSON.stringify(datos, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "preferencias_hydra21.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Mostrar notificación de éxito
    mostrarNotificacion('Preferencias exportadas correctamente', 'success');

    setTimeout(() => {
      URL.revokeObjectURL(url);
      setExportando(false);
    }, 2500);
  };

  // Funcionalidad: Eliminar datos
  const eliminarDatos = () => {
    if (
      !window.confirm(
        "¿Estás seguro de que deseas eliminar todas tus preferencias y datos locales? Esta acción no se puede deshacer."
      )
    )
      return;

    setEliminando(true);

    // Eliminar preferencias generales
    localStorage.removeItem("pref_animaciones");
    localStorage.removeItem("pref_idioma");
    localStorage.removeItem("pref_unidades");
    localStorage.removeItem("pref_formato_fecha");
    localStorage.removeItem("pref_notificaciones");

    // Restablecer valores predeterminados para preferencias generales
    setAnimaciones(true);
    setIdioma("es");
    setUnidades("metric");
    setFormatoFecha("DD/MM/YYYY");
    setNotificaciones(true);

    // Restablecer valores predeterminados para preferencias de sistema
    // Usando el contexto de SystemSettings
    updateSettings({
      performanceMode: "balanced",
      displayMode: "standard",
      cacheEnabled: true,
      developerMode: false
    });

    // Mostrar notificación de éxito
    mostrarNotificacion('Preferencias y datos locales eliminados correctamente', 'success');

    setTimeout(() => {
      setEliminando(false);
    }, 2500);
  };

  // Funcionalidad: Restablecer onboarding
  const [reseteandoOnboarding, setReseteandoOnboarding] = useState(false);

  const restablecerOnboarding = async () => {
    if (
      !window.confirm(
        "¿Estás seguro de que deseas restablecer el estado de onboarding? Esto hará que la pantalla de introducción aparezca nuevamente la próxima vez que recargues la aplicación."
      )
    )
      return;

    setReseteandoOnboarding(true);

    try {
      await storageService.resetOnboarding();

      // Mostrar notificación de éxito
      mostrarNotificacion(
        'Estado de onboarding restablecido. La pantalla de introducción aparecerá la próxima vez que recargues la aplicación.',
        'success',
        3500
      );

      setTimeout(() => {
        setReseteandoOnboarding(false);
      }, 3500);
    } catch (error) {
      console.error("Error al restablecer el estado de onboarding:", error);

      // Mostrar notificación de error
      mostrarNotificacion(
        'Error al restablecer el estado de onboarding.',
        'error',
        3500
      );

      setReseteandoOnboarding(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl sm:text-3xl font-bold text-blue-400">
          Ajustes
        </h1>
        <p className="text-sm text-gray-400">
          Personaliza tu experiencia en HYDRA<sup>21</sup>
        </p>
      </div>

      {/* Tabs for settings sections */}
      <Tabs defaultValue={urlTab === "notifications" ? "notifications" : "general"} className="mb-6">
        <TabsList className="grid grid-cols-4 w-full max-w-md">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="notifications" onClick={() => {
            // Send a test notification when clicking on the notifications tab
            if (isTauri && notificaciones) {
              addNotification(
                "Notificación de prueba",
                "Esta es una notificación de prueba para verificar que el sistema funciona correctamente.",
                "info",
                {
                  priority: "medium",
                  showSystemNotification: notificationSettings.showSystemNotifications
                }
              );
            }
          }}>Notificaciones</TabsTrigger>
          <TabsTrigger value="system">Sistema</TabsTrigger>
          <TabsTrigger value="data">Datos</TabsTrigger>
        </TabsList>

        {/* Tab Contents */}
        <TabsContent value="general" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Sección General */}
            <div className="rounded-lg border border-gray-200 dark:border-blue-900/30 bg-white dark:bg-black/30 shadow-md dark:shadow-none p-6 transition-colors duration-300">
              <div className="flex items-center gap-2 mb-4">
                <Cog6ToothIcon className="h-5 w-5 text-blue-500" />
                <h2 className="text-xl font-semibold text-blue-700 dark:text-white">
                  General
                </h2>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                      Tema
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Cambia entre tema claro y oscuro
                    </p>
                  </div>
                  <ThemeToggle />
                </div>

                <div>
                  <FontSelector />
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                      Animaciones
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Activar o desactivar animaciones
                    </p>
                  </div>
                  <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={animaciones}
                      onChange={(e) => setAnimaciones(e.target.checked)}
                      id="animaciones-toggle"
                    />
                    <label htmlFor="animaciones-toggle">
                      <div
                        className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                          animaciones ? "translate-x-6" : "translate-x-0"
                        }`}
                      ></div>
                    </label>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                      Notificaciones
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Recibir notificaciones del sistema
                    </p>
                  </div>
                  <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={notificaciones}
                      onChange={(e) => setNotificaciones(e.target.checked)}
                      id="notificaciones-toggle"
                    />
                    <label htmlFor="notificaciones-toggle">
                      <div
                        className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                          notificaciones ? "translate-x-6" : "translate-x-0"
                        }`}
                      ></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Sección Preferencias */}
            <div className="rounded-lg border border-gray-200 dark:border-blue-900/30 bg-white dark:bg-black/30 shadow-md dark:shadow-none p-6 transition-colors duration-300">
              <div className="flex items-center gap-2 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5 text-blue-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M16.5 6.75v-.375a2.625 2.625 0 10-5.25 0v.375m7.125 3.375c.621 0 1.125.504 1.125 1.125v7.125A2.625 2.625 0 0116.5 21H7.5a2.625 2.625 0 01-2.625-2.625V11.25c0-.621.504-1.125 1.125-1.125m13.5 0h-13.5"
                  />
                </svg>
                <h2 className="text-xl font-semibold text-blue-700 dark:text-white">
                  Preferencias
                </h2>
              </div>

              <div className="space-y-4">
                <StandardizedSelect
                  label="Idioma"
                  value={idioma}
                  onChange={(value) => setIdioma(value)}
                  options={[
                    { value: "es", label: "Español" },
                    { value: "en", label: "Inglés" }
                  ]}
                />

                <StandardizedSelect
                  label="Unidades de medida"
                  value={unidades}
                  onChange={(value) => setUnidades(value)}
                  options={[
                    { value: "metric", label: "Sistema Métrico (m, kg, s)" },
                    { value: "imperial", label: "Sistema Imperial (ft, lb, gal)" }
                  ]}
                />

                <StandardizedSelect
                  label="Formato de fecha"
                  value={formatoFecha}
                  onChange={(value) => setFormatoFecha(value)}
                  options={[
                    { value: "DD/MM/YYYY", label: "DD/MM/YYYY" },
                    { value: "MM/DD/YYYY", label: "MM/DD/YYYY" },
                    { value: "YYYY-MM-DD", label: "YYYY-MM-DD" }
                  ]}
                />
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="notifications" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Sección de Notificaciones */}
            <div className="rounded-lg border border-gray-200 dark:border-blue-900/30 bg-white dark:bg-black/30 shadow-md dark:shadow-none p-6 transition-colors duration-300">
              <div className="flex items-center gap-2 mb-4">
                <BellRingIcon className="h-5 w-5 text-blue-500" />
                <h2 className="text-xl font-semibold text-blue-700 dark:text-white">
                  Notificaciones
                </h2>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                      Notificaciones del sistema
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Mostrar notificaciones nativas del sistema operativo
                    </p>
                  </div>
                  <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={notificationSettings.showSystemNotifications}
                      onChange={(e) => setNotificationSettings({
                        ...notificationSettings,
                        showSystemNotifications: e.target.checked
                      })}
                      id="system-notifications-toggle"
                    />
                    <label htmlFor="system-notifications-toggle">
                      <div
                        className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                          notificationSettings.showSystemNotifications ? "translate-x-6" : "translate-x-0"
                        }`}
                      ></div>
                    </label>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                      Sonido de notificaciones
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Reproducir sonido al recibir notificaciones
                    </p>
                  </div>
                  <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <input
                      type="checkbox"
                      className="sr-only"
                      checked={notificationSettings.soundEnabled}
                      onChange={(e) => setNotificationSettings({
                        ...notificationSettings,
                        soundEnabled: e.target.checked
                      })}
                      id="sound-notifications-toggle"
                    />
                    <label htmlFor="sound-notifications-toggle">
                      <div
                        className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                          notificationSettings.soundEnabled ? "translate-x-6" : "translate-x-0"
                        }`}
                      ></div>
                    </label>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-200 dark:border-blue-900/30">
                  <h3 className="text-sm font-medium text-blue-700 dark:text-white mb-2">
                    Tipos de notificaciones
                  </h3>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-xs text-gray-700 dark:text-gray-300">
                          Cálculos completados
                        </p>
                      </div>
                      <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={notificationSettings.calculationNotifications}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            calculationNotifications: e.target.checked
                          })}
                          id="calculation-notifications-toggle"
                        />
                        <label htmlFor="calculation-notifications-toggle">
                          <div
                            className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                              notificationSettings.calculationNotifications ? "translate-x-6" : "translate-x-0"
                            }`}
                          ></div>
                        </label>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-xs text-gray-700 dark:text-gray-300">
                          Exportaciones completadas
                        </p>
                      </div>
                      <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={notificationSettings.exportNotifications}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            exportNotifications: e.target.checked
                          })}
                          id="export-notifications-toggle"
                        />
                        <label htmlFor="export-notifications-toggle">
                          <div
                            className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                              notificationSettings.exportNotifications ? "translate-x-6" : "translate-x-0"
                            }`}
                          ></div>
                        </label>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-xs text-gray-700 dark:text-gray-300">
                          Actualizaciones de la aplicación
                        </p>
                      </div>
                      <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={notificationSettings.updateNotifications}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            updateNotifications: e.target.checked
                          })}
                          id="update-notifications-toggle"
                        />
                        <label htmlFor="update-notifications-toggle">
                          <div
                            className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                              notificationSettings.updateNotifications ? "translate-x-6" : "translate-x-0"
                            }`}
                          ></div>
                        </label>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-xs text-gray-700 dark:text-gray-300">
                          Nuevas funcionalidades
                        </p>
                      </div>
                      <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={notificationSettings.featureNotifications}
                          onChange={(e) => setNotificationSettings({
                            ...notificationSettings,
                            featureNotifications: e.target.checked
                          })}
                          id="feature-notifications-toggle"
                        />
                        <label htmlFor="feature-notifications-toggle">
                          <div
                            className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                              notificationSettings.featureNotifications ? "translate-x-6" : "translate-x-0"
                            }`}
                          ></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="pt-2 space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-center text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                    onClick={() => {
                      setSavingNotificationSettings(true);

                      // Save notification settings to backend
                      setTimeout(() => {
                        setSavingNotificationSettings(false);
                        mostrarNotificacion('Configuración de notificaciones guardada', 'success');
                      }, 1000);
                    }}
                    disabled={savingNotificationSettings}
                  >
                    {savingNotificationSettings ? "Guardando..." : "Guardar configuración"}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-center text-sm text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/20"
                    onClick={async () => {
                      if (isTauri) {
                        try {
                          // Test all notification types
                          await NotificationHelper.testNotifications();
                          await NotificationHelper.sendCalculationNotification("Canal Paramétrico", true);
                          await NotificationHelper.sendExportNotification("PDF", true);
                          await NotificationHelper.send3DRenderNotification(true);
                          mostrarNotificacion('Notificaciones de prueba enviadas', 'success');
                        } catch (error) {
                          console.error('Error testing notifications:', error);
                          mostrarNotificacion('Error al probar notificaciones', 'error');
                        }
                      } else {
                        mostrarNotificacion('Las notificaciones solo funcionan en la aplicación de escritorio', 'info');
                      }
                    }}
                  >
                    🔔 Probar Notificaciones
                  </Button>

                  <div className="border-t pt-3 mt-3">
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      🌅 Pantalla de Bienvenida Diaria
                    </h4>
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-center text-sm text-purple-600 dark:text-purple-400 hover:bg-purple-100 dark:hover:bg-purple-900/20"
                        onClick={() => {
                          const debugInfo = dailySplashService.getDebugInfo();
                          console.log('Daily Splash Debug Info:', debugInfo);
                          mostrarNotificacion(
                            `Estado: ${debugInfo.shouldShow ? 'Se mostrará' : 'No se mostrará'} | Fecha: ${debugInfo.currentDate}`,
                            'info'
                          );
                        }}
                      >
                        📊 Ver Estado Actual
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-center text-sm text-orange-600 dark:text-orange-400 hover:bg-orange-100 dark:hover:bg-orange-900/20"
                        onClick={() => {
                          dailySplashService.reset();
                          mostrarNotificacion('Pantalla de bienvenida reiniciada - se mostrará en el próximo inicio', 'success');
                        }}
                      >
                        🔄 Reiniciar Estado
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-center text-sm text-indigo-600 dark:text-indigo-400 hover:bg-indigo-100 dark:hover:bg-indigo-900/20"
                        onClick={() => {
                          // Clear all storage to force splash screen
                          dailySplashService.reset();
                          localStorage.removeItem('hydra21_first_visit');
                          localStorage.removeItem('hydra21_splash_shown');
                          sessionStorage.clear();

                          mostrarNotificacion('Reiniciando para mostrar splash screen...', 'info');
                          setTimeout(() => {
                            window.location.reload();
                          }, 1000);
                        }}
                      >
                        🎬 Probar Splash Screen
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="system" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Sección Sistema */}
            <div className="rounded-lg border border-gray-200 dark:border-blue-900/30 bg-white dark:bg-black/30 shadow-md dark:shadow-none p-6 transition-colors duration-300">
              <div className="flex items-center gap-2 mb-4">
                <ServerIcon className="w-5 h-5 text-blue-500" />
                <h2 className="text-xl font-semibold text-blue-700 dark:text-white">
                  Sistema
                </h2>
              </div>

              <div className="space-y-4">
                {/* Rendimiento de la aplicación */}
                <StandardizedSelect
                  label="Rendimiento"
                  description="Ajusta el rendimiento de la aplicación según tus necesidades"
                  value={settings.performanceMode}
                  onChange={(value) => updateSettings({ performanceMode: value as any })}
                  options={[
                    { value: "performance", label: "Alto rendimiento (mayor consumo de recursos)" },
                    { value: "balanced", label: "Equilibrado (recomendado)" },
                    { value: "efficiency", label: "Eficiencia energética (menor consumo)" }
                  ]}
                />

                {/* Modo de visualización */}
                <StandardizedSelect
                  label="Modo de visualización"
                  description="Configura cómo se muestran los elementos en la aplicación"
                  value={settings.displayMode}
                  onChange={(value) => updateSettings({ displayMode: value as any })}
                  options={[
                    { value: "compact", label: "Compacto (más elementos visibles)" },
                    { value: "standard", label: "Estándar (recomendado)" },
                    { value: "comfortable", label: "Confortable (elementos más espaciados)" }
                  ]}
                />

                {/* Opciones avanzadas */}
                <div>
                  <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                    Opciones avanzadas
                  </h3>

                  {/* Caché de la aplicación */}
                  <div className="flex justify-between items-center mt-2">
                    <div>
                      <p className="text-xs text-gray-700 dark:text-gray-300">
                        Caché de la aplicación
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Almacena datos temporales para mejorar el rendimiento
                      </p>
                    </div>
                    <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                      <input
                        type="checkbox"
                        className="sr-only"
                        checked={settings.cacheEnabled}
                        onChange={(e) => updateSettings({ cacheEnabled: e.target.checked })}
                        id="cache-toggle"
                      />
                      <label htmlFor="cache-toggle">
                        <div
                          className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                            settings.cacheEnabled ? "translate-x-6" : "translate-x-0"
                          }`}
                        ></div>
                      </label>
                    </div>
                  </div>

                  {/* Modo desarrollador */}
                  <div className="flex justify-between items-center mt-3">
                    <div>
                      <p className="text-xs text-gray-700 dark:text-gray-300">
                        Modo desarrollador
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Habilita opciones avanzadas para desarrollo
                      </p>
                    </div>
                    <div className="relative inline-block w-12 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                      <input
                        type="checkbox"
                        className="sr-only"
                        checked={settings.developerMode}
                        onChange={(e) => updateSettings({ developerMode: e.target.checked })}
                        id="dev-mode-toggle"
                      />
                      <label htmlFor="dev-mode-toggle">
                        <div
                          className={`block h-6 w-6 rounded-full bg-blue-500 absolute left-0 top-0 transition-transform transform ${
                            settings.developerMode ? "translate-x-6" : "translate-x-0"
                          }`}
                        ></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="data" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Sección Datos */}
            <div className="rounded-lg border border-gray-200 dark:border-blue-900/30 bg-white dark:bg-black/30 shadow-md dark:shadow-none p-6 transition-colors duration-300">
              <div className="flex items-center gap-2 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5 text-blue-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"
                  />
                </svg>
                <h2 className="text-xl font-semibold text-blue-700 dark:text-white">
                  Datos
                </h2>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                    Almacenamiento
                  </h3>
                  <div className="w-full bg-blue-100 dark:bg-blue-900/30 rounded-full h-2.5 mt-2">
                    <div
                      className="bg-blue-500 h-2.5 rounded-full"
                      style={{ width: "45%" }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    450 MB de 1 GB utilizados
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-blue-700 dark:text-white">
                    Exportar datos
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Descarga todas tus preferencias y datos locales
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 border-blue-200 dark:border-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300"
                    onClick={exportarDatos}
                    disabled={exportando}
                  >
                    {exportando ? "Exportando..." : "Exportar"}
                  </Button>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-red-700 dark:text-white">
                    Eliminar datos
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Elimina todas tus preferencias y datos locales
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 border-red-200 dark:border-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300"
                    onClick={eliminarDatos}
                    disabled={eliminando}
                  >
                    {eliminando ? "Eliminando..." : "Eliminar datos"}
                  </Button>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-orange-700 dark:text-orange-400">
                    Restablecer Onboarding
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Muestra nuevamente la pantalla de introducción al iniciar
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 border-orange-200 dark:border-orange-900/30 text-orange-600 dark:text-orange-400 hover:bg-orange-100 dark:hover:bg-orange-900/20 hover:text-orange-700 dark:hover:text-orange-300 flex items-center"
                    onClick={restablecerOnboarding}
                    disabled={reseteandoOnboarding}
                  >
                    <ArrowPathIcon className="h-4 w-4 mr-1" />
                    {reseteandoOnboarding
                      ? "Restableciendo..."
                      : "Restablecer Onboarding"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
