"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { AnimatePresence } from "framer-motion";
import AnimatedSplashScreen from "./AnimatedSplashScreen";
import OnboardingPage from "./OnboardingPage";
import { storageService } from "@/lib/storage-service";
import { useSystemSettings } from "@/contexts/SystemSettingsContext";
import dailySplashService from "@/lib/daily-splash-service";
import PerformanceMonitor from "./developer/PerformanceMonitor";
import DevInfo from "./developer/DevInfo";
import TitleBar from "./TitleBarNew";
import { useNativeTheme } from "@/hooks/useNativeTheme";

interface AppWrapperProps {
  children: React.ReactNode;
}

const AppWrapper: React.FC<AppWrapperProps> = ({ children }) => {
  // Initialize state to prevent hydration mismatch - all start as false
  const [showSplash, setShowSplash] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [appReady, setAppReady] = useState(false);
  const [hasInitializedOnce, setHasInitializedOnce] = useState(false);
  const { settings } = useSystemSettings();
  const { isMacOS, isWindows, isLinux } = useNativeTheme();
  const pathname = usePathname();

  // Single initialization effect that runs only once
  useEffect(() => {
    // Skip initialization if already done (prevents re-initialization on navigation)
    if (hasInitializedOnce) {
      console.log("🔄 Navigation detected - skipping re-initialization");
      return;
    }

    let isMounted = true;

    const initializeApp = async () => {
      try {
        console.log("🚀 Starting app initialization...");

        // Apply platform-specific classes
        if (typeof window !== "undefined") {
          if (isMacOS) document.body.classList.add('macos');
          if (isWindows) document.body.classList.add('windows');
          if (isLinux) document.body.classList.add('linux');
        }

        // Check if we're in Tauri environment (optimized detection)
        const isTauriEnv = typeof window !== "undefined" &&
                          (window as any).__TAURI__ !== undefined;

        // Initialize storage service
        await storageService.initialize();

        // Initialize daily splash service (fast, synchronous)
        dailySplashService.initialize();

        // Get current app state
        const isFirstVisit = storageService.isFirstVisit();
        const hasSplashBeenShown = storageService.hasSplashBeenShown();
        const onboardingCompleted = await storageService.isOnboardingCompleted();

        // Check daily splash requirements (fast, synchronous)
        const shouldShowDailySplash = dailySplashService.shouldShowSplashToday();

        console.log("📊 App State:", {
          isFirstVisit,
          hasSplashBeenShown,
          onboardingCompleted,
          shouldShowDailySplash,
          isTauriEnv,
          pathname,
          dailySplashDebug: dailySplashService.getDebugInfo()
        });

        // Only update state if component is still mounted
        if (!isMounted) return;

        // Determine what to show based on state - prioritize splash screen display
        if (shouldShowDailySplash || isFirstVisit || !hasSplashBeenShown) {
          // Show splash screen for any of these conditions:
          // 1. Daily splash is required
          // 2. First visit ever
          // 3. Splash hasn't been shown in this session
          console.log("🌅 Showing splash screen - Conditions:", {
            shouldShowDailySplash,
            isFirstVisit,
            hasSplashBeenShown: !hasSplashBeenShown,
            onboardingCompleted
          });
          setShowSplash(true);
          setShowOnboarding(false);
          setAppReady(false);

          // Mark first visit completed if this is first time
          if (isFirstVisit) {
            storageService.markFirstVisitCompleted();
          }
        } else if (!onboardingCompleted) {
          // User needs to complete onboarding
          console.log("👤 Showing onboarding - user hasn't completed setup");
          setShowSplash(false);
          setShowOnboarding(true);
          setAppReady(false);
        } else {
          // Regular user - show app directly
          console.log("✅ Showing app directly - returning user, no splash needed");
          setShowSplash(false);
          setShowOnboarding(false);
          setAppReady(true);
        }

        setIsInitialized(true);
        setHasInitializedOnce(true);
        console.log("🎉 App initialization completed");

      } catch (error) {
        console.error("❌ Error during app initialization:", error);
        if (isMounted) {
          // On error, show app directly to prevent blank screen
          setShowSplash(false);
          setShowOnboarding(false);
          setAppReady(true);
          setIsInitialized(true);
          setHasInitializedOnce(true);
        }
      }
    };

    // Initialize immediately for instant navigation
    initializeApp();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - run only once

  // Fallback timeout - longer to allow splash screen to complete
  useEffect(() => {
    const fallbackTimer = setTimeout(() => {
      if (!isInitialized) {
        console.warn("⚠️ Initialization timeout - forcing app to show");
        setShowSplash(false);
        setShowOnboarding(false);
        setAppReady(true);
        setIsInitialized(true);
      }
    }, 6000); // Increased to allow splash screen to complete (4000ms + buffer)

    return () => clearTimeout(fallbackTimer);
  }, [isInitialized]);

  // Handle splash screen finish
  const handleSplashFinish = async () => {
    try {
      console.log("🎬 Splash screen finished");
      setShowSplash(false);

      // Mark daily splash as shown (fast, synchronous)
      dailySplashService.markSplashShown();
      console.log("🌅 Daily splash marked as shown");

      // Check if we need to show onboarding
      const onboardingCompleted = await storageService.isOnboardingCompleted();
      if (!onboardingCompleted) {
        console.log("📋 Showing onboarding after splash");
        setShowOnboarding(true);
        setAppReady(false);
      } else {
        console.log("✅ Onboarding already completed - showing app");
        setShowOnboarding(false);
        setAppReady(true);
      }
    } catch (error) {
      console.error("❌ Error handling splash finish:", error);
      // On error, show app directly
      setShowSplash(false);
      setShowOnboarding(false);
      setAppReady(true);
    }
  };

  // Handle onboarding completion
  const handleOnboardingComplete = async () => {
    try {
      console.log("✅ Onboarding completed");
      await storageService.setOnboardingCompleted(true);
      setShowOnboarding(false);
      setAppReady(true);
    } catch (error) {
      console.error("❌ Error completing onboarding:", error);
      // Still proceed to show the app
      setShowOnboarding(false);
      setAppReady(true);
    }
  };

  // Debug mode: URL parameters for testing
  useEffect(() => {
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);

      // Force onboarding for testing
      if (urlParams.has("force_onboarding")) {
        console.log("🔧 Debug: Forcing onboarding");
        setShowSplash(false);
        setShowOnboarding(true);
        setAppReady(false);
      }

      // Reset app state for testing
      if (urlParams.has("reset_app")) {
        console.log("🔧 Debug: Resetting app state");
        storageService.resetAppState().then(() => {
          window.location.reload();
        });
      }

      // Show current state for debugging
      if (urlParams.has("debug_state")) {
        console.log("🔧 Debug: Current app state", storageService.getAppState());
      }
    }
  }, []);

  // Navigation effect - ensure app stays ready when navigating
  useEffect(() => {
    if (hasInitializedOnce && !showSplash && !showOnboarding) {
      console.log("🧭 Navigation to:", pathname);
      // Ensure app remains ready during navigation
      if (!appReady) {
        setAppReady(true);
      }
    }
  }, [pathname, hasInitializedOnce, showSplash, showOnboarding, appReady]);

  // Effect to handle app focus and date changes (for overnight scenarios)
  useEffect(() => {
    const handleFocus = () => {
      if (appReady && dailySplashService.checkForDateChange()) {
        console.log("📅 Date change detected while app was running");
        // Don't show splash immediately, but it will show on next launch
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        handleFocus();
      }
    };

    // Listen for focus and visibility changes
    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [appReady]);

  // Render immediately for instant navigation - only show minimal loading if absolutely necessary
  if (!isInitialized && typeof window !== 'undefined') {
    // Only show loading in browser environment and only briefly
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-blue-950">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400 mx-auto mb-2"></div>
          <p className="text-gray-300 text-xs">Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <AnimatePresence mode="wait">
        {showSplash && (
          <AnimatedSplashScreen
            key="splash"
            onFinish={handleSplashFinish}
            duration={4000} // Proper duration for readability
          />
        )}

        {!showSplash && showOnboarding && (
          <OnboardingPage
            key="onboarding"
            onComplete={handleOnboardingComplete}
          />
        )}
      </AnimatePresence>

      {/* Main app content - only show when ready */}
      {appReady && (
        <div
          className={`
            ${settings.displayMode === "compact" ? "display-compact" : ""}
            ${settings.displayMode === "comfortable" ? "display-comfortable" : ""}
            ${settings.performanceMode === "performance" ? "performance-high" : ""}
            ${settings.performanceMode === "efficiency" ? "performance-efficiency" : ""}
            ${settings.developerMode ? "developer-mode" : ""}
            window-root
          `}
          style={{ height: "100%" }}
        >
          {/* Custom title bar for desktop Tauri app */}
          {(isWindows || isMacOS || isLinux) && <TitleBar />}

          {/* Developer tools */}
          {settings.developerMode && <PerformanceMonitor />}
          {settings.developerMode && <DevInfo />}

          {/* Main app content with safe area */}
          <div className="flex-1 overflow-hidden">
            {children}
          </div>
        </div>
      )}
    </>
  );
};

export default AppWrapper;
