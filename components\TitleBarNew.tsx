'use client'

import { useEffect, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'

// Tauri 2.5 compatible types
interface TauriWindow {
  minimize(): Promise<void>
  close(): Promise<void>
  toggleMaximize(): Promise<void>
  isMaximized(): Promise<boolean>
  isFullscreen(): Promise<boolean>
  title(): Promise<string>
  onResized(callback: () => void): Promise<() => void>
  onFocusChanged(callback: () => void): Promise<() => void>
}

// Type guard to check if we're in Tauri environment
const isTauriEnvironment = (): boolean => {
  return typeof window !== 'undefined' &&
         window.__TAURI__ !== undefined &&
         window.__TAURI__ !== null
}

export default function TitleBar () {
  /* ---------- estados ---------- */
  const { theme } = useTheme()
  const [mounted, setMounted]     = useState(false)
  const [isMaximized, setMax]     = useState(false)
  const [isFullscreen, setFull]   = useState(false)
  const [title, setTitle]         = useState('HYDRA²¹ – Desktop Application')
  const [win, setWin]             = useState<TauriWindow | null>(null)

  /* ---------- bootstrap ---------- */
  useEffect(() => {
    setMounted(true)
    if (!isTauriEnvironment()) return          // SSR o modo web

    let cleanup: (() => void)[] = []

    ;(async () => {
      try {
        // Tauri 2.5 compatible window API
        const { getCurrentWindow } = await import('@tauri-apps/api/window')
        const w = getCurrentWindow() as TauriWindow
        setWin(w)

        // estado inicial
        setMax(await w.isMaximized())
        setFull(await w.isFullscreen())
        setTitle(await w.title())

        // listeners con manejo de errores
        try {
          const offResize = await w.onResized(async () => {
            try {
              setMax(await w.isMaximized())
            } catch (error) {
              console.warn('Error updating maximized state:', error)
            }
          })
          cleanup.push(offResize)

          const offFocus = await w.onFocusChanged(async () => {
            try {
              setMax(await w.isMaximized())
            } catch (error) {
              console.warn('Error updating focus state:', error)
            }
          })
          cleanup.push(offFocus)
        } catch (error) {
          console.warn('Error setting up window event listeners:', error)
        }
      } catch (error) {
        console.error('Error initializing titlebar:', error)
      }
    })()

    return () => {
      cleanup.forEach(fn => {
        try {
          fn()
        } catch (error) {
          console.warn('Error during cleanup:', error)
        }
      })
    }
  }, [])

  /* ---------- handlers ---------- */
  const minimize = useCallback(async () => {
    if (!win) return
    try {
      await win.minimize()
    } catch (error) {
      console.error('Error minimizing window:', error)
    }
  }, [win])

  const close = useCallback(async () => {
    if (!win) return
    try {
      await win.close()
    } catch (error) {
      console.error('Error closing window:', error)
    }
  }, [win])

  const toggleMax = useCallback(async () => {
    if (!win) return
    try {
      await win.toggleMaximize()
      setMax(await win.isMaximized())
    } catch (error) {
      console.error('Error toggling maximize:', error)
    }
  }, [win])

  /* ---------- render ---------- */
  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) return null

  // Don't show titlebar in fullscreen mode or if not in Tauri environment
  if (isFullscreen || !isTauriEnvironment()) return null

  const dark = theme === 'dark'

  return (
    <>
      <div data-tauri-drag-region
           onDoubleClick={toggleMax}
           style={{
             height: 32,
             background: dark ? '#0c1222' : '#fff',
             borderBottom: `1px solid ${dark ? '#1e293b' : '#e2e8f0'}`,
             color: dark ? '#f1f5f9' : '#1e293b',
             display: 'flex', alignItems: 'center',
             justifyContent: 'space-between',
             padding: '0 16px',
             userSelect: 'none',
             position: 'fixed', inset: '0 0 auto 0', zIndex: 1000,
             backdropFilter: 'blur(10px)'
           }}>
        {/* título */}
        <div data-tauri-drag-region style={{ flex: 1, fontSize: 13, fontWeight: 600 }}>
          {title}
        </div>

        {/* controles */}
        <div style={{ display: 'flex', gap: 1 }}>
          <Ctrl onClick={minimize}  tip="Minimizar">
            <line x1="2" y1="5" x2="8" y2="5" />
          </Ctrl>

          <Ctrl onClick={toggleMax} tip={isMaximized ? 'Restaurar' : 'Maximizar'}>
            {isMaximized
              ? <polyline points="2.5,4.5 2.5,2.5 7.5,2.5 7.5,4.5 7.5,7.5 2.5,7.5 2.5,4.5" />
              : <rect x="2" y="2" width="6" height="6" />}
          </Ctrl>

          <Ctrl onClick={close}     tip="Cerrar" danger>
            <line x1="2" y1="2" x2="8" y2="8" />
            <line x1="8" y1="2" x2="2" y2="8" />
          </Ctrl>
        </div>
      </div>

      {/* evita que los botones sean zona de drag */}
      <style jsx global>{`
        [data-tauri-drag-region] button {
          -webkit-app-region: no-drag !important;
        }
      `}</style>
    </>
  )
}

/* ---- botón reutilizable ---- */
function Ctrl ({ children, onClick, tip, danger = false }:{
  children: React.ReactNode, onClick: () => void, tip: string, danger?: boolean }) {
  return (
    <button title={tip} onClick={onClick}
      style={{
        width: 46, height: 30, display: 'flex',
        justifyContent: 'center', alignItems: 'center',
        border: 'none', background: 'transparent',
        cursor: 'pointer', borderRadius: 4
      }}
      onMouseEnter={e =>
        (e.currentTarget.style.background = danger ? '#ff5f57' : 'rgba(0,0,0,.06)')}
      onMouseLeave={e => (e.currentTarget.style.background = 'transparent')}>
      <svg width="10" height="10" viewBox="0 0 10 10" fill="none"
           stroke="currentColor" strokeWidth="1" strokeLinecap="round">
        {children}
      </svg>
    </button>
  )
}
