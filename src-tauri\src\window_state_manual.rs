// Implementación manual de persistencia de estado de ventana
// Usando tauri-plugin-store en lugar de window-state
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewWindow, WindowEvent, LogicalSize, LogicalPosition};
use tauri_plugin_store::StoreExt;
use serde::{Serialize, Deserialize};
use serde_json;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowState {
    pub width: f64,
    pub height: f64,
    pub x: i32,
    pub y: i32,
    pub maximized: bool,
    pub fullscreen: bool,
}

impl Default for WindowState {
    fn default() -> Self {
        Self {
            width: 1250.0,
            height: 900.0,
            x: 100,
            y: 100,
            maximized: false,
            fullscreen: false,
        }
    }
}

/// Guarda el estado actual de la ventana
pub async fn save_window_state(window: &WebviewWindow) -> Result<(), String> {
    let app_handle = window.app_handle();

    // Obtener el estado actual de la ventana
    let size = window.inner_size()
        .map_err(|e| format!("Failed to get window size: {}", e))?;

    let position = window.outer_position()
        .map_err(|e| format!("Failed to get window position: {}", e))?;

    let maximized = window.is_maximized()
        .map_err(|e| format!("Failed to get maximized state: {}", e))?;

    let fullscreen = window.is_fullscreen()
        .map_err(|e| format!("Failed to get fullscreen state: {}", e))?;

    let state = WindowState {
        width: size.width as f64,
        height: size.height as f64,
        x: position.x,
        y: position.y,
        maximized,
        fullscreen,
    };

    // Guardar en el store
    let store = app_handle.store("window-state.json")
        .map_err(|e| format!("Failed to get store: {}", e))?;

    // Convertir WindowState a JsonValue usando serde_json
    let state_json = serde_json::to_value(&state)
        .map_err(|e| format!("Failed to serialize window state: {}", e))?;

    store.set("main-window", state_json);

    store.save()
        .map_err(|e| format!("Failed to save store: {}", e))?;

    println!("Window state saved: {:?}", state);
    Ok(())
}

/// Restaura el estado guardado de la ventana (versión silenciosa que no afecta la visibilidad inicial)
pub async fn restore_window_state_silent(window: &WebviewWindow) -> Result<(), String> {
    let app_handle = window.app_handle();

    // Obtener el store
    let store = app_handle.store("window-state.json")
        .map_err(|e| format!("Failed to get store: {}", e))?;

    // Cargar el estado guardado
    let state_json = store.get("main-window");

    if let Some(json_value) = state_json {
        // Deserializar de JsonValue a WindowState
        let state: WindowState = serde_json::from_value(json_value)
            .map_err(|e| format!("Failed to deserialize window state: {}", e))?;
        println!("Restoring window state silently: {:?}", state);

        // Aplicar tamaño y posición solo si no está maximizada
        if !state.maximized && !state.fullscreen {
            // Establecer tamaño
            window.set_size(LogicalSize::new(state.width, state.height))
                .map_err(|e| format!("Failed to set window size: {}", e))?;

            // Establecer posición
            window.set_position(LogicalPosition::new(state.x, state.y))
                .map_err(|e| format!("Failed to set window position: {}", e))?;
        }

        // Aplicar estado de maximizado
        if state.maximized {
            window.maximize()
                .map_err(|e| format!("Failed to maximize window: {}", e))?;
        }

        // Aplicar estado de pantalla completa
        if state.fullscreen {
            window.set_fullscreen(true)
                .map_err(|e| format!("Failed to set fullscreen: {}", e))?;
        }

        // NO llamar window.show() aquí - la ventana ya es visible por configuración
        Ok(())
    } else {
        println!("No saved window state found, keeping default configuration");
        Ok(())
    }
}

/// Restaura el estado guardado de la ventana
pub async fn restore_window_state(window: &WebviewWindow) -> Result<(), String> {
    let app_handle = window.app_handle();

    // Obtener el store
    let store = app_handle.store("window-state.json")
        .map_err(|e| format!("Failed to get store: {}", e))?;

    // Cargar el estado guardado
    let state_json = store.get("main-window");

    if let Some(json_value) = state_json {
        // Deserializar de JsonValue a WindowState
        let state: WindowState = serde_json::from_value(json_value)
            .map_err(|e| format!("Failed to deserialize window state: {}", e))?;
        println!("Restoring window state: {:?}", state);

        // Aplicar tamaño y posición solo si no está maximizada
        if !state.maximized && !state.fullscreen {
            // Establecer tamaño
            window.set_size(LogicalSize::new(state.width, state.height))
                .map_err(|e| format!("Failed to set window size: {}", e))?;

            // Establecer posición
            window.set_position(LogicalPosition::new(state.x, state.y))
                .map_err(|e| format!("Failed to set window position: {}", e))?;
        }

        // Aplicar estado de maximizado
        if state.maximized {
            window.maximize()
                .map_err(|e| format!("Failed to maximize window: {}", e))?;
        }

        // Aplicar estado de pantalla completa
        if state.fullscreen {
            window.set_fullscreen(true)
                .map_err(|e| format!("Failed to set fullscreen: {}", e))?;
        }

        // Mostrar la ventana después de restaurar el estado
        window.show()
            .map_err(|e| format!("Failed to show window: {}", e))?;

        Ok(())
    } else {
        println!("No saved window state found, using defaults");
        // Mostrar la ventana con configuración por defecto
        window.show()
            .map_err(|e| format!("Failed to show window: {}", e))?;
        Ok(())
    }
}

/// Configura el guardado automático del estado de la ventana
pub fn setup_window_state_persistence(window: &WebviewWindow) {
    let window_clone = window.clone();

    // Usar un AtomicBool para evitar múltiples guardados simultáneos
    let is_saving = Arc::new(AtomicBool::new(false));

    // Guardar estado cuando la ventana cambie
    window.on_window_event(move |event| {
        match event {
            WindowEvent::Moved(_) |
            WindowEvent::Resized(_) => {
                // Solo guardar si no hay un guardado en progreso
                let is_saving_clone = is_saving.clone();
                if !is_saving_clone.swap(true, Ordering::SeqCst) {
                    let window = window_clone.clone();
                    tauri::async_runtime::spawn(async move {
                        // Guardar estado
                        let _ = save_window_state(&window).await;
                        // Resetear el flag de guardado
                        is_saving_clone.store(false, Ordering::SeqCst);
                    });
                }
            }
            WindowEvent::CloseRequested { .. } => {
                // Guardar estado final antes de cerrar
                let window = window_clone.clone();
                tauri::async_runtime::block_on(async move {
                    let _ = save_window_state(&window).await;
                });
            }
            _ => {}
        }
    });
}

// Comandos para el frontend
#[tauri::command]
pub async fn save_current_window_state(window: WebviewWindow) -> Result<(), String> {
    save_window_state(&window).await
}

#[tauri::command]
pub async fn restore_saved_window_state(window: WebviewWindow) -> Result<(), String> {
    restore_window_state(&window).await
}

#[tauri::command]
pub async fn get_saved_window_state(app: AppHandle) -> Result<Option<WindowState>, String> {
    let store = app.store("window-state.json")
        .map_err(|e| format!("Failed to get store: {}", e))?;

    let state_json = store.get("main-window");

    if let Some(json_value) = state_json {
        let state: WindowState = serde_json::from_value(json_value)
            .map_err(|e| format!("Failed to deserialize window state: {}", e))?;
        Ok(Some(state))
    } else {
        Ok(None)
    }
}
