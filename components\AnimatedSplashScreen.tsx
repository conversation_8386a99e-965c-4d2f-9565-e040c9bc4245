"use client";

import React, { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface AnimatedSplashScreenProps {
  onFinish: () => void;
  duration?: number;
}

const AnimatedSplashScreen: React.FC<AnimatedSplashScreenProps> = ({
  onFinish,
  duration = 4000,
}) => {
  const [progress, setProgress] = useState(0);
  const [loadingText, setLoadingText] = useState("Inicializando...");
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [stylesLoaded, setStylesLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // Ensure styles are loaded - start immediately with shorter fallback
  useEffect(() => {
    const checkStyles = () => {
      // Check if Tailwind CSS is loaded by testing a known class
      const testElement = document.createElement('div');
      testElement.className = 'bg-primary text-white';
      document.body.appendChild(testElement);

      const computedStyle = window.getComputedStyle(testElement);
      const hasStyles = computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
                       computedStyle.backgroundColor !== 'transparent';

      document.body.removeChild(testElement);

      if (hasStyles) {
        setStylesLoaded(true);
      } else {
        // Retry after a short delay
        setTimeout(checkStyles, 25);
      }
    };

    // Start checking immediately
    checkStyles();
    // Shorter fallback to prevent delays
    const fallbackTimer = setTimeout(() => setStylesLoaded(true), 100);

    return () => clearTimeout(fallbackTimer);
  }, []);

  // Update progress and loading text - only start after styles are loaded
  useEffect(() => {
    if (!stylesLoaded) return;

    const interval = setInterval(() => {
      setProgress((prevProgress) => {
        const newProgress = prevProgress + 1.5; // Slightly slower for better readability

        // Update loading text based on progress
        if (newProgress >= 25 && newProgress < 50) {
          setLoadingText("Cargando componentes...");
        } else if (newProgress >= 50 && newProgress < 75) {
          setLoadingText("Preparando espacio de trabajo...");
        } else if (newProgress >= 75 && newProgress < 95) {
          setLoadingText("Configurando interfaz...");
        } else if (newProgress >= 95) {
          setLoadingText("Iniciando aplicación...");
        }

        if (newProgress >= 100) {
          clearInterval(interval);
          return 100;
        }
        return newProgress;
      });
    }, duration / 70); // More granular updates

    const timer = setTimeout(() => {
      onFinish();
    }, duration);

    return () => {
      clearInterval(interval);
      clearTimeout(timer);
    };
  }, [duration, onFinish, stylesLoaded]);

  // Fallback styles in case Tailwind hasn't loaded
  const fallbackStyles = !stylesLoaded ? {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#000000',
    color: '#ffffff',
    zIndex: 9999,
    fontFamily: 'Rubik, sans-serif'
  } : {};

  return (
    <motion.div
      className={stylesLoaded ? "fixed inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-black via-gray-900 to-blue-950 z-50" : ""}
      style={fallbackStyles}
      initial={{ opacity: 0 }}
      animate={{ opacity: stylesLoaded ? 1 : 0.9 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className={stylesLoaded ? "flex flex-col items-center justify-center space-y-6 p-8 rounded-lg max-w-md w-full" : ""}
        style={!stylesLoaded ? {
          display: 'flex',
          flexDirection: 'column' as const,
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem',
          maxWidth: '28rem',
          width: '100%'
        } : {}}
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: stylesLoaded ? 1 : 0.9 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          delay: stylesLoaded ? 0.2 : 0,
        }}
      >
        {/* Logo with animation */}
        <div className={stylesLoaded ? "flex items-center justify-center mb-4" : ""}
             style={!stylesLoaded ? { display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '1rem' } : {}}>
          <div className={stylesLoaded ? "inline-flex items-center" : ""}
               style={!stylesLoaded ? { display: 'inline-flex', alignItems: 'center' } : {}}>
            <motion.div
              className={stylesLoaded ? "flex items-center justify-center mr-4" : ""}
              style={!stylesLoaded ? { display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '1rem' } : {}}
              initial={{ rotate: -10, opacity: 0 }}
              animate={{
                rotate: 0,
                opacity: 1,
                scale: [1, 1.1, 1],
                y: [0, -5, 0],
              }}
              transition={{
                duration: 1.5,
                ease: "easeOut",
                times: [0, 0.5, 1],
                delay: stylesLoaded ? 0.3 : 0.1,
              }}
            >
              {imageError ? (
                <div
                  className={stylesLoaded ? "h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center shadow-lg" : ""}
                  style={!stylesLoaded ? {
                    height: '5rem',
                    width: '5rem',
                    backgroundColor: '#3B82F6',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3)'
                  } : {}}
                >
                  <span
                    className={stylesLoaded ? "text-white text-3xl font-bold" : ""}
                    style={!stylesLoaded ? { color: '#ffffff', fontSize: '1.875rem', fontWeight: 'bold' } : {}}
                  >
                    H²¹
                  </span>
                </div>
              ) : (
                <div className={stylesLoaded ? "relative h-20 w-20" : ""}
                     style={!stylesLoaded ? { position: 'relative', height: '5rem', width: '5rem' } : {}}>
                  <div
                    className={stylesLoaded ? "absolute inset-0 bg-blue-500/30 rounded-full filter blur-md animate-pulse" : ""}
                    style={!stylesLoaded ? {
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(59, 130, 246, 0.3)',
                      borderRadius: '50%',
                      filter: 'blur(4px)'
                    } : {}}
                  ></div>
                  <img
                    ref={imgRef}
                    src="/pipe_logo.svg"
                    alt="HYDRA21 Logo"
                    className={stylesLoaded ? "h-20 w-20 relative z-10 drop-shadow-lg" : ""}
                    style={!stylesLoaded ? {
                      height: '5rem',
                      width: '5rem',
                      position: 'relative',
                      zIndex: 10,
                      filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
                    } : {
                      height: '5rem',
                      width: '5rem',
                      position: 'relative',
                      zIndex: 10,
                      filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
                    }}
                    onLoad={() => setImageLoaded(true)}
                    onError={() => setImageError(true)}
                  />
                </div>
              )}
            </motion.div>

            <motion.div
              className={stylesLoaded ? "flex items-center" : ""}
              style={!stylesLoaded ? { display: 'flex', alignItems: 'center' } : {}}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: stylesLoaded ? 0.5 : 0.2, duration: 0.8 }}
            >
              <span
                className={stylesLoaded ? "text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 animate-gradient-x" : ""}
                style={!stylesLoaded ? {
                  fontSize: '3.75rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(90deg, #60A5FA 0%, #3B82F6 50%, #2563EB 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  color: 'transparent',
                  backgroundSize: '200% 100%'
                } : { backgroundSize: "200% 100%" }}
              >
                HYDRA
              </span>
              <motion.sup
                className={stylesLoaded ? "text-blue-400 text-3xl font-bold ml-1" : ""}
                style={!stylesLoaded ? {
                  color: '#60A5FA',
                  fontSize: '1.875rem',
                  fontWeight: 'bold',
                  marginLeft: '0.25rem',
                  position: "relative",
                  top: "-0.7em"
                } : { position: "relative", top: "-0.7em" }}
                initial={{ scale: 0, opacity: 0, rotate: 10 }}
                animate={{ scale: 1, opacity: 1, rotate: 0 }}
                transition={{
                  delay: stylesLoaded ? 0.8 : 0.4,
                  duration: 0.7,
                  type: "spring",
                  stiffness: 200,
                }}
              >
                ²¹
              </motion.sup>
            </motion.div>
          </div>
        </div>

        <motion.div
          className={stylesLoaded ? "space-y-3 mt-4" : ""}
          style={!stylesLoaded ? { marginTop: '1rem' } : {}}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: stylesLoaded ? 0.9 : 0.5, duration: 0.8 }}
        >
          <p
            className={stylesLoaded ? "text-gray-300 text-xl text-center font-medium leading-relaxed" : ""}
            style={!stylesLoaded ? {
              color: '#D1D5DB',
              fontSize: '1.25rem',
              textAlign: 'center',
              fontWeight: '500',
              lineHeight: '1.75',
              marginBottom: '0.75rem'
            } : {}}
          >
            Soluciones Avanzadas en Modelación Hidráulica e Ingeniería
            Computacional
          </p>
          <p
            className={stylesLoaded ? "text-gray-400 text-base text-center" : ""}
            style={!stylesLoaded ? {
              color: '#9CA3AF',
              fontSize: '1rem',
              textAlign: 'center'
            } : {}}
          >
            Análisis CFD · Simulación Numérica · Optimización Paramétrica
          </p>
        </motion.div>

        {/* Progress bar with animation */}
        <motion.div
          className={stylesLoaded ? "w-full h-4 bg-gray-800 rounded-full mt-8 overflow-hidden relative shadow-inner" : ""}
          style={!stylesLoaded ? {
            width: '100%',
            height: '1rem',
            backgroundColor: '#1F2937',
            borderRadius: '9999px',
            marginTop: '2rem',
            overflow: 'hidden',
            position: 'relative',
            boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.3)'
          } : {}}
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: "100%", opacity: stylesLoaded ? 1 : 0.9 }}
          transition={{ delay: stylesLoaded ? 1 : 0.6, duration: 0.5 }}
        >
          {/* Efecto de brillo que se mueve a través de la barra */}
          {stylesLoaded && (
            <motion.div
              className="absolute inset-0 w-full h-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
            >
              <motion.div
                className="absolute top-0 bottom-0 w-20 bg-white/20 skew-x-12 filter blur-sm"
                animate={{ left: ["-20%", "120%"] }}
                transition={{
                  repeat: Infinity,
                  duration: 2.5,
                  ease: "easeInOut",
                  repeatDelay: 1,
                }}
              />
            </motion.div>
          )}

          {/* Barra de progreso real */}
          <motion.div
            className={stylesLoaded ? "h-full bg-gradient-to-r from-blue-500 via-blue-400 to-blue-600 rounded-full shadow-lg" : ""}
            style={!stylesLoaded ? {
              height: '100%',
              background: 'linear-gradient(90deg, #3B82F6 0%, #60A5FA 50%, #2563EB 100%)',
              borderRadius: '9999px',
              width: `${progress}%`,
              boxShadow: '0 4px 6px rgba(59, 130, 246, 0.3)'
            } : { width: `${progress}%` }}
            initial={{ width: "0%" }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        </motion.div>

        <motion.p
          className={stylesLoaded ? "text-base text-gray-300 mt-4 font-medium" : ""}
          style={!stylesLoaded ? {
            fontSize: '1rem',
            color: '#D1D5DB',
            marginTop: '1rem',
            fontWeight: '500',
            textAlign: 'center'
          } : {}}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: stylesLoaded ? 1.1 : 0.7 }}
        >
          {loadingText}
        </motion.p>
      </motion.div>

      <motion.div
        className={stylesLoaded ? "absolute bottom-6 text-sm text-gray-400 flex flex-col items-center" : ""}
        style={!stylesLoaded ? {
          position: 'absolute',
          bottom: '1.5rem',
          fontSize: '0.875rem',
          color: '#9CA3AF',
          display: 'flex',
          flexDirection: 'column' as const,
          alignItems: 'center'
        } : {}}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: stylesLoaded ? 1.2 : 0.8, duration: 0.5 }}
      >
        <div
          className={stylesLoaded ? "mb-1 font-medium" : ""}
          style={!stylesLoaded ? { marginBottom: '0.25rem', fontWeight: '500' } : {}}
        >
          Versión 0.1.0-alpha
        </div>
        <div
          className={stylesLoaded ? "text-xs text-gray-500" : ""}
          style={!stylesLoaded ? { fontSize: '0.75rem', color: '#6B7280' } : {}}
        >
          © 2024 HYDRA²¹ Team
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AnimatedSplashScreen;
