// Módulos para la aplicación
mod project;
mod utils;
mod settings;
mod commands;
mod reports;
mod statistics;
mod notifications;
mod math_resolver;
mod visual_effects;
mod fs_helper;
mod window_effects;
mod window_state_manual;

// M<PERSON><PERSON><PERSON> de c<PERSON>lculos
mod calculations;

use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use tauri::Manager;

// Estado para almacenar datos de la aplicación
pub struct AppState {
    pub user_data: Mutex<Option<UserData>>,
}

// Estructura para datos de usuario
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserData {
    pub id: String,
    pub email: Option<String>,
    pub name: Option<String>,
}

// Estructura para archivos recientes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentFile {
    pub path: String,
    pub name: String,
    pub last_opened: String,
}

// Comando para verificar el estado de la aplicación
#[tauri::command]
fn check_app_status() -> String {
    "HYDRA21 está funcionando correctamente".to_string()
}

// Comando para obtener información del sistema
#[tauri::command]
fn get_system_info() -> Result<serde_json::Value, String> {
    // Basic system information with more detailed OS version
    let os_name = std::env::consts::OS;
    let os_family = std::env::consts::FAMILY;
    let arch = std::env::consts::ARCH;
    let hostname = hostname::get().map_or("Unknown".to_string(), |h| h.to_string_lossy().to_string());

    // Get more detailed OS version information
    let mut os_version = os_family.to_string();

    #[cfg(target_os = "windows")]
    {
        use std::process::Command;

        // Try to get Windows version using ver command
        if let Ok(output) = Command::new("cmd").args(&["/C", "ver"]).output() {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                os_version = output_str.trim().to_string();
                println!("Detected OS version: {}", os_version);
            }
        }
    }

    println!("System information:");
    println!("  OS: {}", os_name);
    println!("  OS Version: {}", os_version);
    println!("  Architecture: {}", arch);
    println!("  Hostname: {}", hostname);

    let mut info = serde_json::json!({
        "os": os_name,
        "arch": arch,
        "version": env!("CARGO_PKG_VERSION"),
        "app_name": env!("CARGO_PKG_NAME"),
        "os_version": os_version,
        "hostname": hostname,
        "tauri_version": env!("CARGO_PKG_VERSION"),
        "rust_version": env!("CARGO_PKG_RUST_VERSION", "unknown"),
    });

    // Get IP address information with more details
    if let Ok(interfaces) = get_if_addrs::get_if_addrs() {
        let mut ip_addresses: Vec<String> = Vec::new();
        let mut network_info: Vec<serde_json::Value> = Vec::new();

        println!("Network interfaces:");

        for interface in interfaces.iter() {
            // Skip loopback interfaces
            if !interface.is_loopback() {
                let ip_str = interface.ip().to_string();
                println!("  Interface: {} - IP: {}", interface.name, ip_str);

                ip_addresses.push(ip_str.clone());

                // Add detailed network interface information
                network_info.push(serde_json::json!({
                    "name": interface.name.clone(),
                    "ip": ip_str,
                    "is_ipv4": interface.ip().is_ipv4(),
                    "is_ipv6": interface.ip().is_ipv6(),
                }));
            }
        }

        // Add both simple IP list and detailed network information
        info["ip_addresses"] = serde_json::json!(ip_addresses);
        info["network_interfaces"] = serde_json::json!(network_info);

        println!("Found {} non-loopback network interfaces", ip_addresses.len());
    } else {
        println!("Failed to get network interface information");
    }

    // Get CPU and memory information
    #[cfg(target_os = "windows")]
    {
        use sysinfo::{System, CpuRefreshKind, RefreshKind, MemoryRefreshKind};

        // Create a new system with full refresh capabilities
        let mut sys = System::new_with_specifics(
            RefreshKind::new()
                .with_cpu(CpuRefreshKind::everything())
                .with_memory(MemoryRefreshKind::everything())
        );

        // Refresh all system information
        sys.refresh_all();

        // Wait a moment to get accurate CPU usage
        std::thread::sleep(std::time::Duration::from_millis(200));
        sys.refresh_cpu();
        sys.refresh_memory();

        // CPU information
        let cpu_usage = sys.global_cpu_info().cpu_usage();
        let cpu_brand = sys.global_cpu_info().brand().to_string();

        // Log CPU information for debugging
        println!("CPU Brand: {}", cpu_brand);
        println!("CPU Usage: {}%", cpu_usage);
        println!("CPU Cores: {}", sys.cpus().len());

        // Memory information
        let total_memory = sys.total_memory();
        let used_memory = sys.used_memory();
        let memory_usage_percent = if total_memory > 0 {
            (used_memory as f64 / total_memory as f64) * 100.0
        } else {
            0.0
        };

        // Log memory information for debugging
        println!("Total Memory: {} bytes", total_memory);
        println!("Used Memory: {} bytes", used_memory);
        println!("Memory Usage: {}%", memory_usage_percent);

        // Add to info
        info["cpu"] = serde_json::json!({
            "brand": cpu_brand,
            "usage": cpu_usage,
            "cores": sys.cpus().len(),
        });

        info["memory"] = serde_json::json!({
            "total": total_memory,
            "used": used_memory,
            "usage_percent": memory_usage_percent,
        });

        // Try to get GPU information
        let mut gpu_info = Vec::new();

        // Use WMI to get GPU information
        match wmi::COMLibrary::new() {
            Ok(com_lib) => {
                match wmi::WMIConnection::new(com_lib) {
                    Ok(wmi_con) => {
                        // Define a struct to match the WMI class with more fields
                        #[derive(Debug, serde::Deserialize)]
                        #[allow(non_snake_case)]
                        struct Win32VideoController {
                            Name: Option<String>,
                            DriverVersion: Option<String>,
                            AdapterRAM: Option<u64>,
                            VideoProcessor: Option<String>,
                            CurrentHorizontalResolution: Option<u32>,
                            CurrentVerticalResolution: Option<u32>,
                            CurrentRefreshRate: Option<u32>,
                            Status: Option<String>,
                        }

                        // Query for GPU information
                        match wmi_con.query::<Win32VideoController>() {
                            Ok(results) => {
                                println!("Found {} GPU(s)", results.len());

                                for (i, gpu) in results.iter().enumerate() {
                                    println!("GPU {}: {}", i, gpu.Name.as_ref().unwrap_or(&"Unknown".to_string()));

                                    let name = gpu.Name.clone().unwrap_or_else(|| "Unknown".to_string());
                                    let driver = gpu.DriverVersion.clone().unwrap_or_else(|| "Unknown".to_string());
                                    let memory = gpu.AdapterRAM.unwrap_or(0);

                                    println!("  Name: {}", name);
                                    println!("  Driver: {}", driver);
                                    println!("  Memory: {} bytes", memory);

                                    gpu_info.push(serde_json::json!({
                                        "name": name,
                                        "driver_version": driver,
                                        "memory": memory,
                                        "processor": gpu.VideoProcessor.clone().unwrap_or_else(|| "Unknown".to_string()),
                                        "resolution": format!(
                                            "{}x{} @ {}Hz",
                                            gpu.CurrentHorizontalResolution.unwrap_or(0),
                                            gpu.CurrentVerticalResolution.unwrap_or(0),
                                            gpu.CurrentRefreshRate.unwrap_or(0)
                                        ),
                                        "status": gpu.Status.clone().unwrap_or_else(|| "Unknown".to_string()),
                                    }));
                                }
                            },
                            Err(e) => {
                                println!("Error querying GPU information: {:?}", e);
                                // Fallback to basic system info
                                println!("Using fallback for GPU info");
                                gpu_info.push(serde_json::json!({
                                    "name": "Graphics Device",
                                    "driver_version": "Unknown",
                                    "memory": 0,
                                }));
                            }
                        }
                    },
                    Err(e) => {
                        println!("Error connecting to WMI: {:?}", e);
                    }
                }
            },
            Err(e) => {
                println!("Error initializing COM library: {:?}", e);
            }
        }

        if !gpu_info.is_empty() {
            info["gpu"] = serde_json::json!(gpu_info);
            println!("Added GPU information to response");
        } else {
            println!("No GPU information found");
        }
    }

    Ok(info)
}

// Comando para obtener información del sistema operativo
#[tauri::command]
fn get_system_os_info() -> Result<serde_json::Value, String> {
    // Información básica del sistema operativo
    let name = std::env::consts::OS;
    let _os_family = std::env::consts::FAMILY;
    let _arch = std::env::consts::ARCH;
    let host_name = hostname::get().map_or("Unknown".to_string(), |h| h.to_string_lossy().to_string());

    // Obtener versión del sistema operativo
    let os_version = match std::env::consts::OS {
        "windows" => {
            let output = std::process::Command::new("cmd")
                .args(&["/C", "ver"])
                .output();

            match output {
                Ok(output) => {
                    let version = String::from_utf8_lossy(&output.stdout);
                    version.trim().to_string()
                },
                Err(_) => "Windows (versión desconocida)".to_string()
            }
        },
        "linux" => {
            let output = std::process::Command::new("sh")
                .args(&["-c", "cat /etc/os-release | grep PRETTY_NAME"])
                .output();

            match output {
                Ok(output) => {
                    let version = String::from_utf8_lossy(&output.stdout);
                    if let Some(pretty_name) = version.split('=').nth(1) {
                        pretty_name.trim().trim_matches('"').to_string()
                    } else {
                        "Linux (versión desconocida)".to_string()
                    }
                },
                Err(_) => "Linux (versión desconocida)".to_string()
            }
        },
        "macos" => {
            let output = std::process::Command::new("sh")
                .args(&["-c", "sw_vers -productVersion"])
                .output();

            match output {
                Ok(output) => {
                    let version = String::from_utf8_lossy(&output.stdout);
                    format!("macOS {}", version.trim())
                },
                Err(_) => "macOS (versión desconocida)".to_string()
            }
        },
        _ => format!("{} (versión desconocida)", name)
    };

    // Obtener versión del kernel
    let kernel_version = match std::env::consts::OS {
        "windows" => "N/A".to_string(),
        _ => {
            let output = std::process::Command::new("uname")
                .arg("-r")
                .output();

            match output {
                Ok(output) => String::from_utf8_lossy(&output.stdout).trim().to_string(),
                Err(_) => "Desconocido".to_string()
            }
        }
    };

    // Crear objeto JSON con la información
    Ok(serde_json::json!({
        "name": name,
        "os_version": os_version,
        "kernel_version": kernel_version,
        "host_name": host_name
    }))
}

// Menú nativo - Deshabilitado temporalmente para compatibilidad con Tauri 2.0
// La API de menús ha cambiado en Tauri 2.0
pub fn create_menu() {
    // Esta función está deshabilitada temporalmente
}

// Importar comandos
use crate::commands::{list_files};

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Inicializar el estado de la aplicación
    let app_state = AppState {
        user_data: Mutex::new(None),
    };

    // Configure the thread pool for parallel calculations
    calculations::thread_pool::configure_thread_pool();
    println!("Thread pool configured for parallel calculations");

    // Initialize notification queue
    let notification_queue = std::sync::Arc::new(
        notifications::NotificationQueue::new(100)
    );
    println!("Notification queue initialized");

    // Setup app lifecycle events
    let app_handle_clone = std::sync::Arc::new(std::sync::Mutex::new(None));
    let app_handle_clone_for_setup = app_handle_clone.clone();

    let builder = tauri::Builder::default()
        .manage(app_state)
        .manage(notification_queue)
        .setup(move |app| {
            // Store app handle for later use
            let mut handle = app_handle_clone_for_setup.lock().unwrap();
            *handle = Some(app.handle().clone());

            // Native desktop window setup
            #[cfg(desktop)]
            {
                if let Some(main_window) = app.get_webview_window("main") {
                    // Windows-specific visual effects (non-blocking)
                    #[cfg(target_os = "windows")]
                    {
                        let window_clone = main_window.clone();
                        tauri::async_runtime::spawn(async move {
                            // Try to get HWND using Tauri 2.0 method
                            match window_clone.hwnd() {
                                Ok(hwnd) => {
                                    let hwnd = windows::Win32::Foundation::HWND(hwnd.0 as isize);

                                    // Disable any transparency effects for native desktop feel
                                    if visual_effects::disable_transparency_effects(hwnd) {
                                        println!("Native desktop window configured successfully");
                                    } else {
                                        println!("Could not configure native desktop window");
                                    }
                                }
                                Err(e) => {
                                    println!("Could not get window HWND: {}", e);
                                }
                            }
                        });
                    }

                    // Setup smooth window transitions (cross-platform, non-blocking)
                    window_effects::setup_window_transitions(&main_window);
                    println!("Window transition effects configured");

                    // Configurar persistencia manual del estado de la ventana
                    window_state_manual::setup_window_state_persistence(&main_window);
                    println!("Window state persistence configured");

                    // Restaurar estado guardado de la ventana (non-blocking)
                    let window_clone = main_window.clone();
                    tauri::async_runtime::spawn(async move {
                        // Restore window state without affecting initial visibility
                        if let Err(e) = window_state_manual::restore_window_state_silent(&window_clone).await {
                            eprintln!("Failed to restore window state: {}", e);
                        }
                    });
                }
            }

            // Load notifications from storage and send startup notification
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                match notifications::load_notifications_from_storage(&app_handle).await {
                    Ok(_) => println!("Notifications loaded from storage"),
                    Err(e) => eprintln!("Failed to load notifications: {}", e),
                }

                // Send startup notification
                if let Err(e) = notifications::send_event_notification(
                    &app_handle,
                    "app_start",
                    "HYDRA²¹ Iniciado",
                    "La aplicación se ha iniciado correctamente y está lista para usar."
                ).await {
                    eprintln!("Failed to send startup notification: {}", e);
                }
            });

            Ok(())
        })
        .on_window_event(move |_window, event| {
            if let tauri::WindowEvent::CloseRequested { .. } = event {
                if let Some(app_handle) = app_handle_clone.lock().unwrap().clone() {
                    // Save notifications to storage before exit
                    let app_handle_clone = app_handle.clone();
                    tauri::async_runtime::block_on(async move {
                        match notifications::save_notifications_to_storage(&app_handle_clone).await {
                            Ok(_) => println!("Notifications saved to storage"),
                            Err(e) => eprintln!("Failed to save notifications: {}", e),
                        }
                    });
                }
            }
        });

    // Inicializar solo los plugins que realmente necesitamos
    let builder = builder.plugin(tauri_plugin_dialog::init());
    let builder = builder.plugin(tauri_plugin_notification::init());
    let builder = builder.plugin(tauri_plugin_shell::init());

    // Inicializar plugins store y log
    let builder = builder.plugin(tauri_plugin_store::Builder::default().build());
    let builder = builder.plugin(tauri_plugin_log::Builder::default().build());

    // Inicializar plugin de persistencia de estado de ventana
    // NOTA: Temporalmente comentado - descomenta cuando las dependencias estén instaladas correctamente
    // let builder = builder.plugin(tauri_plugin_window_state::Builder::default().build());

    // Inicializar plugin de sistema de archivos
    // NOTA: Temporalmente comentado - descomenta cuando las dependencias estén instaladas correctamente
    // let builder = builder.plugin(tauri_plugin_fs::init());

    builder
        .invoke_handler(tauri::generate_handler![
            check_app_status,
            get_system_info,
            get_system_os_info,
            list_files,
            project::create_project,
            project::open_project,
            project::save_project,
            settings::get_settings,
            settings::save_settings,
            settings::reset_settings,
            commands::open_file_dialog,
            commands::show_notification,
            commands::show_windows_notification,
            commands::create_new_window,
            commands::create_backup,
            commands::send_bug_report,
            // Register new hydraulic calculation commands
            calculations::hydraulic::canal::calculate_canal_hydraulics,
            calculations::hydraulic::canal::calculate_canal_hydraulics_batch,
            calculations::hydraulic::canal::calculate_canal_depth_range,
            calculations::hydraulic::pipe::calculate_pipe_hydraulics,

            // Register calculator commands
            calculations::calculator::calculate_expression,
            calculations::calculator::calculate_scientific_function,
            calculations::calculator::convert_units,
            calculations::calculator::solve_math_equation,
            calculations::calculator::solve_math_equation_fallback,

            // Register report management commands
            reports::create_report,
            reports::get_report,
            reports::update_report,
            reports::delete_report,
            reports::list_reports,
            reports::export_report_to_pdf,
            reports::generate_report_thumbnail,
            reports::watcher::start_reports_watcher,
            reports::watcher::stop_reports_watcher,

            // Register statistics commands
            statistics::record_system_resources,
            statistics::record_app_usage,
            statistics::get_system_resources,
            statistics::get_app_usage,
            statistics::system_resources::get_current_system_resources,
            statistics::system_resources::start_system_monitoring,
            statistics::app_usage::track_feature_usage,
            statistics::app_usage::track_section_visit,
            statistics::app_usage::track_section_exit,
            statistics::app_usage::track_workflow_step,
            statistics::app_usage::get_feature_usage_stats,
            statistics::app_usage::get_time_spent_stats,
            statistics::app_usage::get_workflow_stats,

            // Register notification commands
            notifications::send_event_notification_command,
            notifications::add_notification,
            notifications::get_notifications,
            notifications::mark_notification_as_read,
            notifications::mark_all_notifications_as_read,
            notifications::delete_notification,
            notifications::clear_notifications,
            notifications::get_unread_notification_count,

            // Register math resolver commands
            math_resolver::init_math_resolver_storage,
            math_resolver::save_math_query,
            math_resolver::get_math_history,
            math_resolver::get_math_query,
            math_resolver::clear_math_history,
            math_resolver::delete_math_query,
            math_resolver::export_math_query_to_pdf,

            // Register file system helper commands
            fs_helper::get_system_directories,
            fs_helper::ensure_project_directory,

            // Register window effects commands
            window_effects::smooth_maximize,
            window_effects::smooth_minimize,
            window_effects::smooth_restore,
            window_effects::get_window_state,

            // Register manual window state commands
            window_state_manual::save_current_window_state,
            window_state_manual::restore_saved_window_state,
            window_state_manual::get_saved_window_state,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
